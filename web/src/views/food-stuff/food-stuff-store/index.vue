<template>
  <CommonPage show-footer title="食材库存管理">
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="api.getFoodStuffStoreList"
    >
      <template #queryBar>
        <QueryBarItem label="食材名称" :label-width="70">
          <NInput
            v-model:value="queryItems.foodStuffName"
            clearable
            type="text"
            placeholder="请输入食材名称"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
      </template>
    </CrudTable>
  </CommonPage>
</template>

<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives } from 'vue'
import {
  NButton,
  useMessage
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudTable from '@/components/table/CrudTable.vue'

import { formatDate, renderIcon } from '@/utils'
import api from '@/api'
import TheIcon from '@/components/icon/TheIcon.vue'

defineOptions({ name: '食材库存管理' })

const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')
const message = useMessage()

const columns = [
  {
    title: '食材名称',
    key: 'foodStuffName',
    width: 150,
    align: 'center',
    ellipsis: { tooltip: true }
  },
  {
    title: '供应商名称',
    key: 'supplierName',
    width: 150,
    align: 'center',
    ellipsis: { tooltip: true }
  },
  {
    title: '库存数量',
    key: 'store_count',
    width: 150,
    align: 'center',
    render(row) {
      return h('span', `${row.store_count}`)
    }
  },
  {
    title: '单位',
    key: 'unitName',
    width: 100,
    align: 'center'
  },
  {
    title: '更新时间',
    key: 'updatedAt',
    width: 180,
    align: 'center',
    render(row) {
      return h('span', formatDate(row.updatedAt))
    }
  }
]

// 库存数量现在只显示，不支持直接修改

onMounted(() => {
  $table.value?.handleSearch()
})
</script>