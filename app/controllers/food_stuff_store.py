from tortoise.expressions import Q
from tortoise.transactions import atomic
from datetime import datetime
from contextvars import copy_context

from app.controllers.utils import get_school_id
from app.core.crud import CRUDBase
from app.core.ctx import CTX_USER_ID
from app.models.admin import Dept, DeptType
from app.models.orders import Order, OrderStatus
from app.models.food_stuff import FoodStuffStore, FoodStuffStoreRecord, FoodStuff, Unit
from app.schemas.food_stuff_store import FoodStuffStoreCreate, FoodStuffStoreUpdate, FoodStuffStoreRecordCreate, FoodStuffStoreRecordSearch
from app.controllers.user import user_controller


class FoodStuffStoreController(CRUDBase[FoodStuffStore, FoodStuffStoreCreate, FoodStuffStoreUpdate]):
    def __init__(self):
        super().__init__(model=FoodStuffStore)

    async def get_list(self, search: dict):
        """获取库存列表 - 兼容旧接口"""
        # 构建查询条件
        q = Q()
        if search.get("food_stuff_id"):
            q &= Q(food_stuff_id=search["food_stuff_id"])
        if search.get("food_stuff_ids"):
            food_stuff_ids = search["food_stuff_ids"].split(",")
            q &= Q(food_stuff_id__in=food_stuff_ids)
        if search.get("school_id"):
            q &= Q(school_id=search["school_id"])
        if search.get("store_count"):
            q &= Q(store_count=search["store_count"])

        # 查询库存数据
        if search.get("food_stuff_name"):
            # 先查询符合名称条件的食材
            food_stuffs = await FoodStuff.filter(food_stuff_name__contains=search["food_stuff_name"])
            if not food_stuffs:
                return {"total": 0, "items": []}

            # 获取食材ID列表
            food_stuff_ids = [fs.id for fs in food_stuffs]
            # 使用ID列表查询库存
            q &= Q(food_stuff_id__in=food_stuff_ids)

        # 获取总数
        total = await self.model.filter(q).count()
        
        # 获取分页数据
        store_items = await self.model.filter(q).offset((search["current"] - 1) * search["size"]).limit(search["size"])
        
        # 批量获取关联数据以提高性能
        food_stuff_ids = [store.food_stuff_id for store in store_items]
        supplier_ids = [store.supplier_id for store in store_items if store.supplier_id]
        
        # 批量查询食材信息
        food_stuffs = await FoodStuff.filter(id__in=food_stuff_ids)
        food_stuffs_dict = {fs.id: fs for fs in food_stuffs}
        
        # 批量查询单位信息
        unit_ids = [fs.food_stuff_unit_id for fs in food_stuffs if fs.food_stuff_unit_id]
        units = await Unit.filter(id__in=unit_ids)
        units_dict = {unit.id: unit.unit_name for unit in units}
        
        # 批量查询供应商信息
        suppliers = await Dept.filter(id__in=supplier_ids)
        suppliers_dict = {supplier.id: supplier.name for supplier in suppliers}
        
        # 构建返回数据
        result_items = []
        for store in store_items:
            food_stuff = food_stuffs_dict.get(store.food_stuff_id)
            if not food_stuff:
                continue
                
            item_dict = {
                "id": store.id,
                "foodStuffId": store.food_stuff_id,
                "foodStuffName": food_stuff.food_stuff_name,
                "schoolId": store.school_id,
                "supplierId": store.supplier_id,
                "supplierName": suppliers_dict.get(store.supplier_id, ""),
                "storeCount": store.store_count,
                "unitName": units_dict.get(food_stuff.food_stuff_unit_id, ""),
                "updatedAt": store.updated_at.strftime("%Y-%m-%d %H:%M:%S") if store.updated_at else ""
            }
            result_items.append(item_dict)

        return {"total": total, "items": result_items}
    
    async def list(self, *, page: int = 1, page_size: int = 10, search: Q = None):
        """标准列表方法 - 参考food_stuff.py的实现"""
        if search is None:
            search = Q()
        
        # 获取当前用户所属学校
        school_id = await get_school_id()
        search &= Q(school_id=school_id)
        
        # 获取总数
        total = await self.model.filter(search).count()
        
        # 获取分页数据
        items = await self.model.filter(search).offset((page - 1) * page_size).limit(page_size)
        
        # 批量获取关联数据以提高性能
        food_stuff_ids = [item.food_stuff_id for item in items]
        supplier_ids = [item.supplier_id for item in items if item.supplier_id]
        
        # 批量查询食材信息
        food_stuffs = await FoodStuff.filter(id__in=food_stuff_ids)
        food_stuffs_dict = {fs.id: fs for fs in food_stuffs}
        
        # 批量查询单位信息
        unit_ids = [fs.food_stuff_unit_id for fs in food_stuffs if fs.food_stuff_unit_id]
        units = await Unit.filter(id__in=unit_ids)
        units_dict = {unit.id: unit.unit_name for unit in units}
        
        # 批量查询供应商信息
        suppliers = await Dept.filter(id__in=supplier_ids)
        suppliers_dict = {supplier.id: supplier.name for supplier in suppliers}
        
        # 为每个库存项添加关联信息
        for item in items:
            food_stuff = food_stuffs_dict.get(item.food_stuff_id)
            if food_stuff and food_stuff.food_stuff_unit_id:
                item.unit_name = units_dict.get(food_stuff.food_stuff_unit_id, "")
                item.food_stuff_name = food_stuff.food_stuff_name
            else:
                item.unit_name = ""
                item.food_stuff_name = ""
            
            # 添加供应商信息
            item.supplier_name = suppliers_dict.get(item.supplier_id, "")
        
        return total, items

    @atomic()
    async def create_store(self, obj_in: FoodStuffStoreCreate):
        #获取当前学校id
        school_id = await get_school_id()
        # 检查是否已存在相同食材、学校和供应商的记录
        existing_store = await self.model.filter(
            id=obj_in.id,
            food_stuff_id=obj_in.food_stuff_id,
            school_id=school_id,
            supplier_id=obj_in.supplier_id
        ).first()

        if existing_store:
            raise ValueError("食材库存已存在")
        else:
            # 如果不存在，则创建新记录
            return await self.create(obj_in=obj_in)

    async def record_store_change(self, operation_type: str, remark: str = None, food_stuff_changes: list = None, school_id: int = None):
        """快捷方法：记录库存变化
        Args:
            operation_type: 操作类型
            remark: 操作备注
            food_stuff_changes: 食材变化列表，格式: [{"food_stuff_id": 1, "before_count": 100, "change_count": 50}]
        """
        if not school_id:
            school_id = await get_school_id()
        
        # 获取当前用户ID
        ctx = copy_context()
        operator_id = ctx.get(CTX_USER_ID, None)
        
        # 构建完整的食材数据
        food_stuff_data = []
        for change in food_stuff_changes or []:
            # 获取食材信息
            food_stuff = await FoodStuff.get(id=change["food_stuff_id"])
            
            # 获取单位信息
            unit_name = ""
            if hasattr(food_stuff, "food_stuff_unit_id") and food_stuff.food_stuff_unit_id:
                unit = await Unit.get_or_none(id=food_stuff.food_stuff_unit_id)
                if unit:
                    unit_name = unit.unit_name
            
            food_item = {
                "food_stuff_id": change["food_stuff_id"],
                "food_stuff_name": food_stuff.food_stuff_name,
                "before_count": change["before_count"],
                "change_count": change["change_count"],
                "after_count": change["before_count"] + change["change_count"],
                "unit_name": unit_name
            }
            food_stuff_data.append(food_item)
        
        record_data = FoodStuffStoreRecordCreate(
            foodStuffData=food_stuff_data,
            operationType=operation_type,
            remark=remark,
            schoolId=school_id,
            operatorId=operator_id
        )
        
        record = FoodStuffStoreRecord(**record_data.model_dump(by_alias=False))
        await record.save()
        return record
    
    @atomic()
    async def update_store(self, obj_in):
        school_id = await get_school_id()
        # 如果传入的是模型类，先转换为dict
        if hasattr(obj_in, "model_dump"):
            update_data = obj_in.model_dump(exclude_unset=True)
        else:
            update_data = obj_in

        # 获取ID
        store_id = update_data.get("id")
        if not store_id:
            raise ValueError("库存ID不能为空")

        # 查询库存记录
        obj = await self.get(id=store_id, school_id=school_id)
        if not obj:
            raise ValueError("食材库存不存在")
        
        # 记录变化前的数量
        before_count = obj.store_count
        
        # 更新库存数量
        new_count = None
        if "storeCount" in update_data:
            new_count = update_data["storeCount"]
        elif "store_count" in update_data:
            new_count = update_data["store_count"]
        
        if new_count is not None:
            change_count = new_count - before_count
            obj.store_count = new_count
            
            # 记录库存变化
            await self.record_store_change(
                operation_type="手动调整",
                remark="库存数量手动调整",
                food_stuff_changes=[{
                    "food_stuff_id": obj.food_stuff_id,
                    "before_count": before_count,
                    "change_count": change_count
                }]
            )

        # 保存更新
        await obj.save()
        return obj
    
    @atomic()
    async def confirm_receive(self, order_id: str, token: str):
        # 获取用户所属部门信息
        user_dept = await Dept.filter(token=token).first()
        if not user_dept:
            raise Exception("token不正确")
        if user_dept.type != DeptType.SCHOOL:
            raise Exception("当前用户不是学校")
    
        # 查询订单 - 支持通过ID或订单编号查询
        try:
            # 尝试将order_id转换为整数（如果是数字ID）
            id_value = int(order_id)
            order = await Order.get_or_none(id=id_value, school_id=user_dept.id)
        except ValueError:
            # 如果不是数字，则按订单编号查询
            order = await Order.get_or_none(order_number=order_id, school_id=user_dept.id)
    
        if not order:
            raise ValueError("订单不存在或不属于当前学校")
    
        # 验证订单状态
        if order.order_status == OrderStatus.CONFIRMED:
            raise ValueError("订单已经入库，无法重复确认入库")
        elif order.order_status != OrderStatus.SHIPPED:
            raise ValueError("订单状态不是已出库，无法确认入库")
    
        # 初始化变化记录列表
        food_stuff_changes = []
        immediate_consume_changes = []
    
        # 遍历订单内的食材，更新库存
        for item in order.order_items:
            food_stuff_id = item.get("id")
            quantity = item.get("quantity", 0)
            immediate_consume = item.get("immediate_consume", False)
            consume_quantity = item.get("consume_quantity", 0)
    
            if not food_stuff_id or not quantity:
                continue
    
            # 获取食材信息以获取supplier_id
            food_stuff = await FoodStuff.get_or_none(id=food_stuff_id)
            if not food_stuff:
                continue
                
            # 查找是否已有该食材的库存
            existing_store = await self.model.filter(
                food_stuff_id=food_stuff_id,
                school_id=user_dept.id,
                supplier_id=food_stuff.supplier_id
            ).first()
    
            before_count = 0
            if existing_store:
                before_count = existing_store.store_count
                # 更新现有库存
                existing_store.store_count += float(quantity)
                await existing_store.save()
            else:
                # 创建新库存记录
                new_store = FoodStuffStore(
                    food_stuff_id=food_stuff_id,
                    store_count=float(quantity),
                    school_id=user_dept.id,
                    supplier_id=food_stuff.supplier_id
                )
                await new_store.save()
                existing_store = new_store
            
            # 收集入库变化信息
            food_stuff_changes.append({
                "food_stuff_id": food_stuff_id,
                "before_count": before_count,
                "change_count": float(quantity)
            })
            
            # 如果有立即消耗标记，则在入库后立即扣除相应库存
            if immediate_consume and consume_quantity > 0:
                # 获取入库后的库存数量
                after_receive_count = existing_store.store_count
                
                # 扣除立即消耗的数量
                consume_amount = min(float(consume_quantity), after_receive_count)
                existing_store.store_count -= consume_amount
                await existing_store.save()
                
                # 收集立即消耗的变化信息
                immediate_consume_changes.append({
                    "food_stuff_id": food_stuff_id,
                    "before_count": after_receive_count,
                    "change_count": -consume_amount
                })
        
        # 记录入库的库存变化
        if food_stuff_changes:
            await self.record_store_change(
                operation_type="入库",
                remark=f"订单{order.order_number}确认入库",
                food_stuff_changes=food_stuff_changes,
                school_id=user_dept.id,
            )
        
        # 记录立即消耗的库存变化
        if immediate_consume_changes:
            await self.record_store_change(
                operation_type="下单库存消耗",
                remark=f"订单{order.order_number}入库后立即消耗",
                food_stuff_changes=immediate_consume_changes,
                school_id=user_dept.id,
            )
    
        # 更新订单状态为已入库
        order.order_status = OrderStatus.CONFIRMED
        order.order_confirm_date = datetime.now()
        await order.save()
    
        return True




    async def get_store_records(self, search: FoodStuffStoreRecordSearch):
        """获取库存变化记录列表"""
        school_id = await get_school_id()
        q = Q(school_id=school_id)
        
        if search.operation_type:
            q &= Q(operation_type=search.operation_type)
        if search.start_date:
            q &= Q(created_at__gte=search.start_date)
        if search.end_date:
            q &= Q(created_at__lte=search.end_date)
        
        total = await FoodStuffStoreRecord.filter(q).count()
        records = await FoodStuffStoreRecord.filter(q).order_by('-created_at').offset(
            (search.current - 1) * search.size
        ).limit(search.size)
        
        # 为每个记录添加额外信息
        for record in records:
            # 可以在这里添加关联数据处理，类似 food_stuff.py 中的单位信息处理
            pass
        
        return total, records

food_stuff_store_controller = FoodStuffStoreController()