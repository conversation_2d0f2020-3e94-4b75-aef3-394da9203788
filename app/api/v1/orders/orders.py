import logging
from typing import Any

from pydantic import BaseModel
from app.core.ctx import CTX_USER_ID
from fastapi import APIRouter, Body, Query
from tortoise.expressions import Q

from app.controllers.order import order_controller
from app.controllers.user import user_controller
from app.models.admin import Dept
from app.schemas.base import Success, Fail,SuccessExtra
from app.schemas.orders import OrderCreate
from app.models.orders import OrderStatus, OrderType
from app.models.admin import DeptType, User
from app.controllers.utils import get_school_id
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/create", summary="创建订单")
async def create_order(
    order_in: OrderCreate,
):
    result = await order_controller.create_order(obj_in=order_in)
    return Success(data=result, msg="下单处理完成")

@router.post("/create-direct", summary="直接创建订单（不检查库存）")
async def create_direct_order(
    order_in: OrderCreate,
):
    """
    直接创建订单，不检查库存，不扣除库存
    用于食材管理页面的购物车下单
    """
    result = await order_controller.create_direct_order(obj_in=order_in)
    return Success(data=result, msg="直接下单成功")

@router.get("/list", summary="获取订单列表")
async def get_order_list(
    order_type: OrderType = Query(None, description="订单类型", alias="orderType"),
    order_status: OrderStatus = Query(None, description="订单状态", alias="orderStatus"),
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    order_number: str = Query(None, description="订单编号", alias="orderNumber"),
    supplier_id: int = Query(None, description="供应商ID", alias="supplierId"),
):
    q = Q()
    if order_type:
        q &= Q(order_type=order_type)
    if order_status:
        q &= Q(order_status=order_status)
    if order_number:
        q &= Q(order_number__contains=order_number)
    if supplier_id:
        q &= Q(supplier_id=supplier_id)
    # 获取当前用户所属部门
    user_id = CTX_USER_ID.get()
    user_obj: User = await user_controller.get(id=user_id)
    if not user_obj.dept_id and not user_obj.is_superuser:
        return Fail(msg="用户没有所属部门")

    # 获取用户所属部门信息
    user_dept = await Dept.filter(id=user_obj.dept_id).first()

    # 检查部门类型是否为学校
    if user_dept and user_dept.type == DeptType.SCHOOL:
        q &= Q(school_id=user_dept.id)
    elif user_dept and user_dept.type == DeptType.SUPPLY:
        q &= Q(supplier_id=user_dept.id)
    elif not user_obj.is_superuser:
        return Fail(msg="非学校和供应商不能查看订单")

    total, items = await order_controller.get_order_list(page=page, page_size=page_size, search=q)
    return SuccessExtra(data=items, total=total, page=page, page_size=page_size)

class Shipment(BaseModel):
    order_id: int
    order_items: list[Any]

@router.post("/shipment", summary="发货")
async def shipment(
    shipment: Shipment = Body(..., description="发货信息"),
):
    try:
        await order_controller.shipment(order_id=shipment.order_id, order_items=shipment.order_items)
        return Success(msg="发货成功")
    except Exception as e:
        return Fail(msg=str(e))

@router.get("/detail", summary="获取订单详情")
async def get_order_detail(
    order_id: int = Query(..., description="订单ID"),
):
    """获取订单详情，用于管理端查看订单详情"""
    try:
        order = await order_controller.get(id=order_id)
        if not order:
            return Fail(msg="订单不存在")
        
        # 获取当前用户所属部门
        user_id = CTX_USER_ID.get()
        user_obj: User = await user_controller.get(id=user_id)
        
        # 权限检查：只有超级管理员、订单所属学校或供应商可以查看
        if not user_obj.is_superuser:
            if not user_obj.dept_id:
                return Fail(msg="用户没有所属部门")
            
            user_dept = await Dept.filter(id=user_obj.dept_id).first()
            if not user_dept:
                return Fail(msg="用户部门不存在")
            
            # 检查是否有权限查看此订单
            if user_dept.type == DeptType.SCHOOL and order.school_id != user_dept.id:
                return Fail(msg="无权查看此订单")
            elif user_dept.type == DeptType.SUPPLY and order.supplier_id != user_dept.id:
                return Fail(msg="无权查看此订单")
            elif user_dept.type not in [DeptType.SCHOOL, DeptType.SUPPLY]:
                return Fail(msg="无权查看订单")
        
        order_dict = await order.to_dict()
        return Success(data=order_dict, msg="获取订单详情成功")
    except Exception as e:
        logger.error(f"获取订单详情失败: {str(e)}")
        return Fail(msg=f"获取订单详情失败: {str(e)}")
